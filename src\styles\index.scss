@use "./reset";
@use "./element-plus";
// Vxe Table
@use "./vxe-table";
@import url("./vxe-table.css");

.app-container {
  padding: 15px;
}

// 进度条颜色
#nprogress .bar {
  background-color: var(--el-color-primary);
}

// 混合布局左侧菜单的hover样式
.layout-mix .layout__sidebar--left .el-menu {
  .el-menu-item {
    &:hover {
      // 极简白主题：使用浅灰色背景
      background-color: var(--el-fill-color-light) !important;
    }
  }

  .el-sub-menu__title {
    &:hover {
      // 极简白主题：使用浅灰色背景
      background-color: var(--el-fill-color-light) !important;
    }
  }
}

// 深色主题或深蓝色侧边栏配色下的左侧菜单hover样式
html.dark .layout-mix .layout__sidebar--left .el-menu,
html.sidebar-color-blue .layout-mix .layout__sidebar--left .el-menu {
  .el-menu-item {
    &:hover {
      // 深色背景：使用CSS变量
      background-color: var(--menu-hover) !important;
    }
  }

  .el-sub-menu__title {
    &:hover {
      // 深色背景：使用CSS变量
      background-color: var(--menu-hover) !important;
    }
  }
}

// 窄屏时隐藏菜单文字，只显示图标
.hideSidebar {
  // Top布局和Mix布局的水平菜单
  &.layout-top .layout__header .el-menu--horizontal,
  &.layout-mix .layout__header .el-menu--horizontal {
    .el-menu-item,
    .el-sub-menu__title {
      .menu-title,
      span:not([class*="i-svg"]):not(.el-icon) {
        display: none !important;
      }
    }
  }

  // Mix布局的左侧菜单
  &.layout-mix .layout__sidebar--left .el-menu {
    .el-menu-item,
    .el-sub-menu__title {
      .menu-title,
      span:not([class*="i-svg"]):not(.el-icon) {
        display: none !important;
      }
    }
  }
}

// 全局搜索区域样式
.search-container {
  padding: 18px 16px 0;
  margin-bottom: 16px;
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;

  .search-buttons {
    margin-right: 0;
  }

  .el-form-item {
    margin-bottom: 18px;
  }
}

// 表格区域样式
.data-table {
  margin-bottom: 16px;

  // 表格工具栏区域
  &__toolbar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    &--actions,
    &--tools {
      display: flex;
      gap: 8px;
    }
  }

  // 表格内容区域
  &__content {
    margin: 8px 0;
  }

  // 分页区域
  .el-pagination {
    justify-content: flex-end;
    margin-top: 16px;
  }
}
