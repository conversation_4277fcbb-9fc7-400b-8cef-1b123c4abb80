<template>
  <div :class="['navbar-actions', navbarActionsClass]">
    <!-- 桌面端工具项 -->
    <template v-if="isDesktop">
      <!-- 搜索 -->
      <div class="navbar-actions__item">
        <MenuSearch />
      </div>

      <!-- 全屏 -->
      <!-- <div class="navbar-actions__item">
        <Fullscreen />
      </div> -->

      <!-- 布局大小 -->
      <!-- <div class="navbar-actions__item">
        <SizeSelect />
      </div> -->

      <!-- 通知 -->
      <div class="navbar-actions__item">
        <Notification />
      </div>
          <!-- 主题设置（移动至 Navbar） -->
    <div v-if="defaultSettings.showSettings" class="navbar-actions__item">
      <el-dropdown trigger="click" placement="bottom-end" :hide-on-click="false">
        <div class="i-svg:menu" />
        <template #dropdown>
          <div class="theme-panel p-3 w-260px" @click.stop>
            <div class="theme-panel__row flex-x-between mb-2">
              <span class="text-xs">暗黑模式</span>
              <el-switch v-model="isDark" active-icon="Moon" inactive-icon="Sunny" @change="handleThemeChange" />
            </div>
            <div class="theme-panel__row flex-x-between mb-2">
              <span class="text-xs">主题颜色</span>
              <el-color-picker v-model="selectedThemeColor" :predefine="colorPresets" popper-class="theme-picker-dropdown" />
            </div>
            <div v-if="!isDark" class="theme-panel__row">
              <span class="text-xs mr-2">侧边栏配色</span>
              <el-radio-group v-model="sidebarColor" size="small" @change="changeSidebarColor">
                <el-radio :value="SidebarColor.CLASSIC_BLUE">经典蓝</el-radio>
                <el-radio :value="SidebarColor.MINIMAL_WHITE">极简白</el-radio>
              </el-radio-group>
            </div>
          </div>
        </template>
      </el-dropdown>
    </div>
    </template>

    <!-- 用户菜单 -->
    <div class="navbar-actions__item">
      <el-dropdown trigger="click">
        <div class="user-profile">
          <img class="user-profile__avatar" :src="userStore.userInfo.avatar" />
          <span class="user-profile__name">{{ userStore.userInfo.username }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleProfileClick">
              用户资料
            </el-dropdown-item>
            <el-dropdown-item divided @click="logout">
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
// 移除了多语言支持
import { useRoute, useRouter } from "vue-router";
import { defaultSettings, themeColorPresets } from "@/settings";
import { DeviceEnum } from "@/enums/settings/device.enum";
import { useAppStore, useSettingsStore, useUserStore } from "@/store";
import { SidebarColor, ThemeMode } from "@/enums/settings/theme.enum";
import { LayoutMode } from "@/enums";

// 导入子组件
import MenuSearch from "@/components/MenuSearch/index.vue";
import Fullscreen from "@/components/Fullscreen/index.vue";
import SizeSelect from "@/components/SizeSelect/index.vue";
// 移除了语言选择组件
import Notification from "@/components/Notification/index.vue";
const appStore = useAppStore();
const settingStore = useSettingsStore();
const userStore = useUserStore();

const route = useRoute();
const router = useRouter();

// 是否为桌面设备
const isDesktop = computed(() => appStore.device === DeviceEnum.DESKTOP);

/**
 * 打开个人中心页面
 */
function handleProfileClick() {
  router.push({ name: "Profile" });
}

// 根据主题和侧边栏配色方案选择样式类
const navbarActionsClass = computed(() => {
  const { theme, sidebarColorScheme, layout } = settingStore;

  // 暗黑主题下，所有布局都使用白色文字
  if (theme === ThemeMode.DARK) {
    return "navbar-actions--white-text";
  }

  // 明亮主题下
  if (theme === ThemeMode.LIGHT) {
    // 顶部布局和混合布局的顶部区域：
    // - 如果侧边栏是经典蓝色，使用白色文字
    // - 如果侧边栏是极简白色，使用深色文字
    if (layout === LayoutMode.TOP || layout === LayoutMode.MIX) {
      if (sidebarColorScheme === SidebarColor.CLASSIC_BLUE) {
        return "navbar-actions--white-text";
      } else {
        return "navbar-actions--dark-text";
      }
    }
  }

  return "navbar-actions--dark-text";
});

/**
 * 退出登录
 */
function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    lockScroll: false,
  }).then(() => {
    userStore.logout().then(() => {
      router.push(`/login?redirect=${route.fullPath}`);
    });
  });
}

// 主题设置逻辑（与设置抽屉保持一致）
const isDark = ref<boolean>(settingStore.theme === ThemeMode.DARK);
const sidebarColor = ref(settingStore.sidebarColorScheme);
const colorPresets = themeColorPresets;
const selectedThemeColor = computed({
  get: () => settingStore.themeColor,
  set: (value) => settingStore.updateThemeColor(value),
});

function handleThemeChange(val: string | number | boolean) {
  settingStore.updateTheme(val ? ThemeMode.DARK : ThemeMode.LIGHT);
}

function changeSidebarColor(val: any) {
  settingStore.updateSidebarColorScheme(val);
}
</script>

<style lang="scss" scoped>
.navbar-actions {
  display: flex;
  align-items: center;
  height: 100%;

  &__item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px; /* 增加最小点击区域到44px，符合人机交互标准 */
    height: 100%;
    min-height: 44px;
    padding: 0 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;

    // 确保子元素居中
    > * {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    // 确保 Element Plus 组件可以正常工作
    :deep(.el-dropdown),
    :deep(.el-tooltip) {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    // 图标样式
    :deep([class^="i-svg:"]) {
      font-size: 18px;
      line-height: 1;
      color: var(--el-text-color-regular);
      transition: color 0.3s;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.04);

      :deep([class^="i-svg:"]) {
        color: var(--el-color-primary);
      }
    }
  }

  .user-profile {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 8px;

    &__avatar {
      flex-shrink: 0;
      width: 28px;
      height: 28px;
      border-radius: 50%;
    }

    &__name {
      margin-left: 8px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
      transition: color 0.3s;
    }
  }
}

// 主题设置面板样式
.theme-panel {
  min-width: 240px;
  .theme-panel__row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

// 确保颜色选择器下拉面板正确显示
:deep(.theme-picker-dropdown) {
  z-index: 9999 !important;
}

// 白色文字样式（用于深色背景：暗黑主题、顶部布局、混合布局）
.navbar-actions--white-text {
  .navbar-actions__item {
    :deep([class^="i-svg:"]) {
      color: rgba(255, 255, 255, 0.85);
    }

    &:hover {
      background: rgba(255, 255, 255, 0.1);

      :deep([class^="i-svg:"]) {
        color: #fff;
      }
    }
  }

  .user-profile__name {
    color: rgba(255, 255, 255, 0.85);
  }
}

// 深色文字样式（用于浅色背景：明亮主题下的左侧布局）
.navbar-actions--dark-text {
  .navbar-actions__item {
    :deep([class^="i-svg:"]) {
      color: var(--el-text-color-regular) !important;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.04);

      :deep([class^="i-svg:"]) {
        color: var(--el-color-primary) !important;
      }
    }
  }

  .user-profile__name {
    color: var(--el-text-color-regular) !important;
  }
}

// 确保下拉菜单中的图标不受影响
:deep(.el-dropdown-menu) {
  [class^="i-svg:"] {
    color: var(--el-text-color-regular) !important;

    &:hover {
      color: var(--el-color-primary) !important;
    }
  }
}
</style>
